@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义样式 */
.prose {
  @apply max-w-none;
}

.prose h1 {
  @apply text-3xl font-bold text-gray-900 mb-4;
}

.prose h2 {
  @apply text-2xl font-semibold text-gray-800 mb-3 mt-6;
}

.prose h3 {
  @apply text-xl font-medium text-gray-700 mb-2 mt-4;
}

.prose p {
  @apply text-gray-600 mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside mb-4 text-gray-600;
}

.prose ol {
  @apply list-decimal list-inside mb-4 text-gray-600;
}

.prose li {
  @apply mb-1;
}

.prose blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-700 mb-4;
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
}

.prose pre code {
  @apply bg-transparent p-0;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 页面过渡动画 */
.page-transition {
  @apply animate-fade-in;
}

/* 悬浮效果 */
.hover-lift {
  @apply transition-all duration-200 ease-in-out;
}

.hover-lift:hover {
  @apply transform -translate-y-1 shadow-medium;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-xl shadow-soft border border-gray-100 p-6 hover-lift;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.card-header {
  @apply border-b border-gray-100 pb-4 mb-6;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-description {
  @apply text-sm text-gray-600 mt-1;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 表格样式 */
.table {
  @apply w-full border-collapse;
}

.table th {
  @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

.table tr:hover {
  @apply bg-gray-50;
}

/* 状态标签 */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.draft {
  @apply bg-gray-100 text-gray-800;
}

.status-badge.published {
  @apply bg-green-100 text-green-800;
}

.status-badge.archived {
  @apply bg-red-100 text-red-800;
}

.status-badge.active {
  @apply bg-blue-100 text-blue-800;
}

.status-badge.inactive {
  @apply bg-gray-100 text-gray-800;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    @apply hidden;
  }
  
  .main-content {
    @apply ml-0;
  }
}
