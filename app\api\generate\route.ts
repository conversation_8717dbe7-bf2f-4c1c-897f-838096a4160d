import { NextRequest, NextResponse } from 'next/server'
import { aiManager } from '@/lib/ai'
import { supabaseService } from '@/lib/supabase'
import { PostGenerationParams } from '@/types'
import { slugify, calculateReadingTime, countWords } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const params: PostGenerationParams = body

    // 验证必需参数
    if (!params.keywords || params.keywords.length === 0) {
      return NextResponse.json(
        { success: false, error: '关键词不能为空' },
        { status: 400 }
      )
    }

    // 生成博文内容
    const generatedContent = await aiManager.generateBlogPost(params)

    // 计算额外信息
    const wordCount = countWords(generatedContent.content)
    const readingTime = calculateReadingTime(generatedContent.content)
    const slug = slugify(generatedContent.title)

    // 生成AI总结（用于系列关联）
    const aiSummary = await generatePostSummary(generatedContent.content)

    const result = {
      ...generatedContent,
      slug,
      word_count: wordCount,
      reading_time: readingTime,
      ai_summary: aiSummary,
      generation_params: params,
    }

    return NextResponse.json({
      success: true,
      data: result,
    })
  } catch (error) {
    console.error('博文生成失败:', error)
    return NextResponse.json(
      { success: false, error: '生成失败，请重试' },
      { status: 500 }
    )
  }
}

// 生成博文总结
async function generatePostSummary(content: string): Promise<string> {
  try {
    const response = await aiManager.generateContent({
      prompt: `请为以下博文内容生成一个简洁的总结（100字以内）：\n\n${content.substring(0, 1000)}...`,
      provider: 'openai',
      temperature: 0.5,
      max_tokens: 200,
    })
    return response.content.trim()
  } catch (error) {
    console.error('生成总结失败:', error)
    return content.substring(0, 200) + '...'
  }
}
