import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'
import { CreatePromptForm } from '@/types'

// 获取Prompt列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('project_id') || 'default'
    const status = searchParams.get('status')
    const category = searchParams.get('category')

    // 模拟数据 - 实际项目中从数据库获取
    const mockPrompts = [
      {
        id: '1',
        project_id: projectId,
        name: '博文生成模板',
        description: '用于生成高质量SEO博文的通用模板',
        content: `请根据以下关键词生成一篇高质量的SEO博文：

关键词: {keywords}
语言: {language}
标题: {title}

请按照以下JSON格式返回结果：
{
  "title": "博文标题",
  "content": "博文正文内容（使用Markdown格式）",
  "excerpt": "博文摘要（100-150字）",
  "meta_title": "SEO标题（50-60字符）",
  "meta_description": "SEO描述（150-160字符）",
  "tags": ["标签1", "标签2", "标签3"]
}

要求：
1. 内容要原创、有价值、结构清晰
2. 自然融入关键词，避免关键词堆砌
3. 标题要吸引人且包含主要关键词
4. 内容长度在800-1500字之间
5. 使用适当的标题层级（H2、H3等）
6. 包含实用的信息和见解`,
        variables: {
          keywords: { type: 'array', description: '关键词列表' },
          language: { type: 'string', description: '语言代码' },
          title: { type: 'string', description: '自定义标题（可选）' }
        },
        category: 'blog',
        language: 'zh',
        status: 'active',
        usage_count: 15,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '2',
        project_id: projectId,
        name: 'SEO优化模板',
        description: '专门用于优化博文SEO信息的模板',
        content: `请为以下博文生成SEO优化信息：

标题：{title}
内容：{content}
语言：{language}

请返回JSON格式：
{
  "meta_title": "SEO标题（50-60字符，包含主要关键词）",
  "meta_description": "SEO描述（150-160字符，吸引点击）",
  "meta_keywords": "关键词（用逗号分隔）"
}

要求：
1. SEO标题要包含主要关键词且吸引人
2. 描述要简洁明了，突出价值
3. 关键词要相关且不过度堆砌`,
        variables: {
          title: { type: 'string', description: '博文标题' },
          content: { type: 'string', description: '博文内容' },
          language: { type: 'string', description: '语言代码' }
        },
        category: 'seo',
        language: 'zh',
        status: 'active',
        usage_count: 8,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '3',
        project_id: projectId,
        name: '标题生成模板',
        description: '根据关键词生成吸引人的博文标题',
        content: `请为以下关键词生成5个吸引人的博文标题：

关键词：{keywords}
语言：{language}
风格：{style}

请返回JSON数组格式：
["标题1", "标题2", "标题3", "标题4", "标题5"]

要求：
1. 标题要包含主要关键词
2. 具有吸引力和点击欲望
3. 长度适中（20-60字符）
4. 符合目标语言的表达习惯
5. 避免过度夸张或误导性内容`,
        variables: {
          keywords: { type: 'array', description: '关键词列表' },
          language: { type: 'string', description: '语言代码' },
          style: { type: 'string', description: '标题风格（专业/通俗/创意等）' }
        },
        category: 'title',
        language: 'zh',
        status: 'active',
        usage_count: 12,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    ]

    // 应用筛选
    let filteredPrompts = mockPrompts
    if (status && status !== 'all') {
      filteredPrompts = filteredPrompts.filter(p => p.status === status)
    }
    if (category && category !== 'all') {
      filteredPrompts = filteredPrompts.filter(p => p.category === category)
    }

    return NextResponse.json({
      success: true,
      data: filteredPrompts,
    })
  } catch (error) {
    console.error('获取Prompt列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取Prompt列表失败' },
      { status: 500 }
    )
  }
}

// 创建新Prompt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const promptData: CreatePromptForm & {
      project_id: string
      usage_count?: number
    } = body

    // 验证必需字段
    if (!promptData.name || !promptData.content) {
      return NextResponse.json(
        { success: false, error: '名称和内容不能为空' },
        { status: 400 }
      )
    }

    // 设置默认值
    if (!promptData.project_id) {
      promptData.project_id = 'default'
    }
    if (!promptData.language) {
      promptData.language = 'zh'
    }
    if (!promptData.usage_count) {
      promptData.usage_count = 0
    }

    // 模拟创建Prompt
    const newPrompt = {
      id: Date.now().toString(),
      ...promptData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('创建Prompt:', newPrompt)

    return NextResponse.json({
      success: true,
      data: newPrompt,
      message: 'Prompt创建成功',
    })
  } catch (error) {
    console.error('创建Prompt失败:', error)
    return NextResponse.json(
      { success: false, error: '创建Prompt失败' },
      { status: 500 }
    )
  }
}
