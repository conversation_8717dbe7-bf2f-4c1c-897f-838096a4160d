export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          database_url: string | null
          api_key: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          database_url?: string | null
          api_key?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          database_url?: string | null
          api_key?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      authors: {
        Row: {
          id: string
          project_id: string
          name: string
          bio: string | null
          avatar_url: string | null
          email: string | null
          website: string | null
          social_links: Json | null
          expertise: Json | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          bio?: string | null
          avatar_url?: string | null
          email?: string | null
          website?: string | null
          social_links?: Json | null
          expertise?: Json | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          bio?: string | null
          avatar_url?: string | null
          email?: string | null
          website?: string | null
          social_links?: Json | null
          expertise?: Json | null
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      prompts: {
        Row: {
          id: string
          project_id: string
          name: string
          description: string | null
          content: string
          variables: Json | null
          category: string | null
          language: string
          status: string
          usage_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          description?: string | null
          content: string
          variables?: Json | null
          category?: string | null
          language?: string
          status?: string
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          description?: string | null
          content?: string
          variables?: Json | null
          category?: string | null
          language?: string
          status?: string
          usage_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      series: {
        Row: {
          id: string
          project_id: string
          name: string
          description: string | null
          summary: string | null
          total_posts: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          description?: string | null
          summary?: string | null
          total_posts?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          description?: string | null
          summary?: string | null
          total_posts?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      posts: {
        Row: {
          id: string
          project_id: string
          series_id: string | null
          author_id: string | null
          prompt_id: string | null
          title: string
          slug: string | null
          content: string
          excerpt: string | null
          keywords: Json | null
          language: string
          meta_title: string | null
          meta_description: string | null
          meta_keywords: string | null
          category: string | null
          tags: Json | null
          status: string
          published_at: string | null
          generation_params: Json | null
          ai_summary: string | null
          word_count: number | null
          reading_time: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          series_id?: string | null
          author_id?: string | null
          prompt_id?: string | null
          title: string
          slug?: string | null
          content: string
          excerpt?: string | null
          keywords?: Json | null
          language?: string
          meta_title?: string | null
          meta_description?: string | null
          meta_keywords?: string | null
          category?: string | null
          tags?: Json | null
          status?: string
          published_at?: string | null
          generation_params?: Json | null
          ai_summary?: string | null
          word_count?: number | null
          reading_time?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          series_id?: string | null
          author_id?: string | null
          prompt_id?: string | null
          title?: string
          slug?: string | null
          content?: string
          excerpt?: string | null
          keywords?: Json | null
          language?: string
          meta_title?: string | null
          meta_description?: string | null
          meta_keywords?: string | null
          category?: string | null
          tags?: Json | null
          status?: string
          published_at?: string | null
          generation_params?: Json | null
          ai_summary?: string | null
          word_count?: number | null
          reading_time?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      series_posts: {
        Row: {
          id: string
          series_id: string
          post_id: string
          order_index: number
          created_at: string
        }
        Insert: {
          id?: string
          series_id: string
          post_id: string
          order_index: number
          created_at?: string
        }
        Update: {
          id?: string
          series_id?: string
          post_id?: string
          order_index?: number
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
