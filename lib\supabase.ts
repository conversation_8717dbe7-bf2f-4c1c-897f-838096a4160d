import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

// 环境变量
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// 客户端 Supabase 实例（用于客户端组件）
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// 服务端 Supabase 实例（用于服务端组件）
export const createServerSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerComponentClient<Database>({ cookies: () => cookieStore })
}

// 客户端组件 Supabase 实例
export const createClientSupabaseClient = () => {
  return createClientComponentClient<Database>()
}

// 数据库操作工具函数
export class SupabaseService {
  private client: ReturnType<typeof createClient<Database>>

  constructor(client?: ReturnType<typeof createClient<Database>>) {
    this.client = client || supabase
  }

  // 项目相关操作
  async getProjects() {
    const { data, error } = await this.client
      .from('projects')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  async createProject(project: Omit<Database['public']['Tables']['projects']['Insert'], 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await this.client
      .from('projects')
      .insert(project)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // 作者相关操作
  async getAuthors(projectId: string) {
    const { data, error } = await this.client
      .from('authors')
      .select('*')
      .eq('project_id', projectId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  async createAuthor(author: Omit<Database['public']['Tables']['authors']['Insert'], 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await this.client
      .from('authors')
      .insert(author)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // Prompt相关操作
  async getPrompts(projectId: string) {
    const { data, error } = await this.client
      .from('prompts')
      .select('*')
      .eq('project_id', projectId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  async createPrompt(prompt: Omit<Database['public']['Tables']['prompts']['Insert'], 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await this.client
      .from('prompts')
      .insert(prompt)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // 系列相关操作
  async getSeries(projectId: string) {
    const { data, error } = await this.client
      .from('series')
      .select('*')
      .eq('project_id', projectId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  async createSeries(series: Omit<Database['public']['Tables']['series']['Insert'], 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await this.client
      .from('series')
      .insert(series)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // 博文相关操作
  async getPosts(projectId: string, filters?: {
    status?: string
    seriesId?: string
    authorId?: string
    limit?: number
  }) {
    let query = this.client
      .from('posts')
      .select(`
        *,
        author:authors(*),
        series:series(*),
        prompt:prompts(*)
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.seriesId) {
      query = query.eq('series_id', filters.seriesId)
    }
    if (filters?.authorId) {
      query = query.eq('author_id', filters.authorId)
    }
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }

    const { data, error } = await query
    if (error) throw error
    return data
  }

  async createPost(post: Omit<Database['public']['Tables']['posts']['Insert'], 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await this.client
      .from('posts')
      .insert(post)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  async updatePost(id: string, updates: Partial<Database['public']['Tables']['posts']['Update']>) {
    const { data, error } = await this.client
      .from('posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  async deletePost(id: string) {
    const { error } = await this.client
      .from('posts')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// 默认服务实例
export const supabaseService = new SupabaseService()
