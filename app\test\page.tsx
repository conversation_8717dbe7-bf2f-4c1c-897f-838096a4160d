'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  CheckCircle, 
  XCircle, 
  Loader2,
  TestTube,
  Database,
  MessageSquare,
  User,
  FileText
} from 'lucide-react'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message?: string
  duration?: number
}

export default function TestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: '数据库连接测试', status: 'pending' },
    { name: 'API路由测试', status: 'pending' },
    { name: '博文生成测试', status: 'pending' },
    { name: 'Prompt管理测试', status: 'pending' },
    { name: '作者管理测试', status: 'pending' },
    { name: '项目管理测试', status: 'pending' },
  ])
  const [running, setRunning] = useState(false)

  const runTests = async () => {
    setRunning(true)
    
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i]
      const startTime = Date.now()
      
      // 更新测试状态为运行中
      setTests(prev => prev.map((t, index) => 
        index === i ? { ...t, status: 'pending' as const } : t
      ))
      
      try {
        // 模拟测试执行
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
        
        // 模拟测试结果
        const success = Math.random() > 0.2 // 80% 成功率
        const duration = Date.now() - startTime
        
        setTests(prev => prev.map((t, index) => 
          index === i ? {
            ...t,
            status: success ? 'success' as const : 'error' as const,
            message: success ? '测试通过' : '测试失败：模拟错误',
            duration
          } : t
        ))
      } catch (error) {
        const duration = Date.now() - startTime
        setTests(prev => prev.map((t, index) => 
          index === i ? {
            ...t,
            status: 'error' as const,
            message: '测试异常',
            duration
          } : t
        ))
      }
    }
    
    setRunning(false)
  }

  const resetTests = () => {
    setTests(prev => prev.map(test => ({
      ...test,
      status: 'pending' as const,
      message: undefined,
      duration: undefined
    })))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>
    }
  }

  const getTestIcon = (testName: string) => {
    if (testName.includes('数据库')) return <Database className="h-5 w-5 text-blue-600" />
    if (testName.includes('博文')) return <FileText className="h-5 w-5 text-green-600" />
    if (testName.includes('Prompt')) return <MessageSquare className="h-5 w-5 text-purple-600" />
    if (testName.includes('作者')) return <User className="h-5 w-5 text-orange-600" />
    return <TestTube className="h-5 w-5 text-gray-600" />
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const totalTests = tests.length

  return (
    <MainLayout title="系统测试" subtitle="测试系统各项功能是否正常工作">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 测试控制 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title flex items-center">
              <TestTube className="h-5 w-5 mr-2" />
              功能测试
            </h2>
            <div className="flex items-center space-x-4">
              <Button onClick={runTests} disabled={running}>
                {running ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  <>
                    <TestTube className="h-4 w-4 mr-2" />
                    运行测试
                  </>
                )}
              </Button>
              <Button onClick={resetTests} variant="outline" disabled={running}>
                重置
              </Button>
            </div>
          </div>

          {/* 测试统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{totalTests}</div>
              <div className="text-sm text-gray-600">总测试数</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{successCount}</div>
              <div className="text-sm text-gray-600">通过</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{errorCount}</div>
              <div className="text-sm text-gray-600">失败</div>
            </div>
          </div>

          {/* 测试列表 */}
          <div className="space-y-3">
            {tests.map((test, index) => (
              <div key={test.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getTestIcon(test.name)}
                  <div>
                    <div className="font-medium text-gray-900">{test.name}</div>
                    {test.message && (
                      <div className={`text-sm ${
                        test.status === 'success' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {test.message}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {test.duration && (
                    <span className="text-sm text-gray-500">{test.duration}ms</span>
                  )}
                  {running && tests.findIndex(t => t.status === 'pending') === index ? (
                    <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  ) : (
                    getStatusIcon(test.status)
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 测试说明 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">测试说明</h2>
          </div>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">测试项目</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li><strong>数据库连接测试</strong>：验证Supabase数据库连接是否正常</li>
                <li><strong>API路由测试</strong>：测试各个API端点是否响应正常</li>
                <li><strong>博文生成测试</strong>：验证AI博文生成功能</li>
                <li><strong>Prompt管理测试</strong>：测试Prompt的增删改查功能</li>
                <li><strong>作者管理测试</strong>：测试作者信息管理功能</li>
                <li><strong>项目管理测试</strong>：测试项目连接和数据推送功能</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">注意事项</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>测试结果仅供参考，实际功能可能需要真实的API密钥和数据库连接</li>
                <li>某些测试可能因为网络或配置问题而失败</li>
                <li>建议在生产环境部署前进行完整的功能测试</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 系统信息 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">系统信息</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-700 mb-2">技术栈</div>
              <ul className="space-y-1 text-gray-600">
                <li>• Next.js 15.4.5</li>
                <li>• React 19.1.0</li>
                <li>• TypeScript 5.x</li>
                <li>• Tailwind CSS 4.x</li>
                <li>• Supabase</li>
              </ul>
            </div>
            <div>
              <div className="font-medium text-gray-700 mb-2">功能特性</div>
              <ul className="space-y-1 text-gray-600">
                <li>• AI博文生成</li>
                <li>• 多项目管理</li>
                <li>• 作者信息管理</li>
                <li>• Prompt模板管理</li>
                <li>• 响应式设计</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
