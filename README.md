# Auto Blog SaaS - AI自动博文生成系统

一个基于Next.js和AI技术的自动博文生成SaaS平台，帮助用户快速生成高质量的SEO博文。

## ✨ 功能特性

### 🤖 博文生成
- **AI驱动**：使用先进的AI技术生成高质量博文内容
- **关键词优化**：支持基于关键词的内容生成
- **多语言支持**：支持中文、英文、日文等多种语言
- **SEO优化**：自动生成SEO标题、描述和关键词
- **系列管理**：支持博文系列，保持内容连贯性

### 📝 内容管理
- **博文管理**：完整的博文增删改查功能
- **状态管理**：草稿、已发布、已归档等状态管理
- **预览编辑**：实时预览和编辑功能
- **批量操作**：支持批量管理博文

### 🎯 Prompt管理
- **模板系统**：可自定义AI生成模板
- **变量支持**：支持动态变量替换
- **分类管理**：按用途分类管理Prompt
- **使用统计**：跟踪Prompt使用情况

### 👥 作者管理
- **多作者支持**：支持多个作者信息管理
- **详细资料**：包含头像、简介、专业领域等
- **社交链接**：支持多种社交媒体链接
- **状态控制**：可启用/禁用作者

### 🏗️ 项目管理
- **多项目支持**：管理多个项目的数据库连接
- **数据同步**：支持项目间数据推送
- **连接测试**：实时测试数据库连接状态
- **配置管理**：独立的项目配置和API密钥

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm、yarn、pnpm 或 bun

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd auto-blog
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

3. **环境配置**
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置以下环境变量：
```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI API 配置 (可选)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
```

4. **数据库设置**
在Supabase中执行 `database/schema.sql` 文件来创建数据库表结构。

5. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

6. **访问应用**
打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
auto-blog/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── generate/          # 博文生成页面
│   ├── posts/             # 博文管理页面
│   ├── prompts/           # Prompt管理页面
│   ├── authors/           # 作者管理页面
│   ├── projects/          # 项目管理页面
│   └── test/              # 测试页面
├── components/            # React 组件
│   ├── ui/                # 基础UI组件
│   └── layout/            # 布局组件
├── lib/                   # 工具库
│   ├── supabase.ts        # Supabase 配置
│   ├── ai.ts              # AI 服务
│   └── utils.ts           # 工具函数
├── types/                 # TypeScript 类型定义
├── database/              # 数据库相关文件
└── public/                # 静态资源
```

## 🛠️ 技术栈

- **前端框架**：Next.js 15.4.5
- **UI库**：React 19.1.0
- **样式**：Tailwind CSS 4.x
- **类型检查**：TypeScript 5.x
- **数据库**：Supabase (PostgreSQL)
- **UI组件**：Radix UI
- **图标**：Lucide React
- **AI集成**：OpenAI、Anthropic、Google AI

## 📖 使用指南

### 1. 配置作者信息
首次使用时，建议先在"作者管理"中添加作者信息，包括姓名、简介、专业领域等。

### 2. 创建Prompt模板
在"Prompt管理"中创建AI生成模板，可以自定义生成规则和格式。

### 3. 生成博文
在"博文生成"页面：
- 输入关键词
- 选择语言和分类
- 选择作者和Prompt模板
- 点击生成博文

### 4. 管理内容
在"博文管理"中可以查看、编辑、发布或删除生成的博文。

### 5. 项目管理
在"项目管理"中配置多个项目的数据库连接，实现数据同步。

## 🧪 测试

访问 `/test` 页面可以运行系统功能测试，验证各个模块是否正常工作。

## 📝 开发说明

### 添加新的AI提供商
在 `lib/ai.ts` 中添加新的AI服务类，并在AIManager中注册。

### 自定义UI组件
所有UI组件都在 `components/ui/` 目录中，基于Radix UI构建。

### 数据库扩展
如需修改数据库结构，请更新 `database/schema.sql` 和相应的TypeScript类型。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License

## 🆘 支持

如果您在使用过程中遇到问题，请：
1. 查看项目文档
2. 运行系统测试页面检查功能状态
3. 提交Issue描述问题

---

**Auto Blog SaaS** - 让AI为您的内容创作赋能 🚀
