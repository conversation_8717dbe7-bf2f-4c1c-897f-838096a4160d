'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Save, 
  Eye,
  Plus,
  X,
  HelpCircle
} from 'lucide-react'
import { PROMPT_CATEGORIES, SUPPORTED_LANGUAGES } from '@/types'

export default function NewPromptPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    content: '',
    category: '',
    language: 'zh',
    variables: [] as Array<{ name: string; type: string; description: string }>,
  })
  const [saving, setSaving] = useState(false)
  const [newVariable, setNewVariable] = useState({
    name: '',
    type: 'string',
    description: '',
  })

  const addVariable = () => {
    if (newVariable.name.trim()) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, { ...newVariable }]
      }))
      setNewVariable({ name: '', type: 'string', description: '' })
    }
  }

  const removeVariable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter((_, i) => i !== index)
    }))
  }

  const handleSave = async () => {
    if (!formData.name.trim() || !formData.content.trim()) {
      alert('请填写名称和内容')
      return
    }

    setSaving(true)
    try {
      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          project_id: 'default',
          variables: formData.variables.reduce((acc, variable) => {
            acc[variable.name] = {
              type: variable.type,
              description: variable.description,
            }
            return acc
          }, {} as Record<string, any>),
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        alert('Prompt创建成功！')
        router.push('/prompts')
      } else {
        throw new Error(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建失败:', error)
      alert('创建失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout title="创建Prompt" subtitle="创建新的AI生成模板">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <Link href="/prompts">
            <Button variant="ghost">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回列表
            </Button>
          </Link>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              预览
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <div className="loading-spinner mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 基本信息 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">基本信息</h2>
            <p className="card-description">设置Prompt的基本信息</p>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  名称 *
                </label>
                <Input
                  placeholder="输入Prompt名称"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  分类
                </label>
                <Select value={formData.category} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, category: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {PROMPT_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                描述
              </label>
              <Textarea
                placeholder="描述这个Prompt的用途和特点"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                语言
              </label>
              <Select value={formData.language} onValueChange={(value) => 
                setFormData(prev => ({ ...prev, language: value }))
              }>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 变量定义 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title flex items-center">
              变量定义
              <HelpCircle className="h-4 w-4 ml-2 text-gray-400" />
            </h2>
            <p className="card-description">
              定义Prompt中使用的变量，使用 {'{变量名}'} 的格式在内容中引用
            </p>
          </div>

          <div className="space-y-4">
            {/* 添加变量 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <Input
                placeholder="变量名"
                value={newVariable.name}
                onChange={(e) => setNewVariable(prev => ({ ...prev, name: e.target.value }))}
              />
              <Select value={newVariable.type} onValueChange={(value) => 
                setNewVariable(prev => ({ ...prev, type: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="string">字符串</SelectItem>
                  <SelectItem value="array">数组</SelectItem>
                  <SelectItem value="number">数字</SelectItem>
                  <SelectItem value="boolean">布尔值</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="描述"
                value={newVariable.description}
                onChange={(e) => setNewVariable(prev => ({ ...prev, description: e.target.value }))}
              />
              <Button onClick={addVariable} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* 变量列表 */}
            {formData.variables.length > 0 && (
              <div className="space-y-2">
                {formData.variables.map((variable, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <code className="px-2 py-1 bg-gray-100 rounded text-sm">
                          {'{' + variable.name + '}'}
                        </code>
                        <span className="text-sm text-gray-600">{variable.type}</span>
                        <span className="text-sm text-gray-500">{variable.description}</span>
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => removeVariable(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Prompt内容 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Prompt内容</h2>
            <p className="card-description">
              编写Prompt内容，使用 {'{变量名}'} 引用上面定义的变量
            </p>
          </div>

          <div>
            <Textarea
              placeholder="输入Prompt内容..."
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              rows={15}
              className="font-mono text-sm"
            />
          </div>
        </div>

        {/* 示例 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">使用示例</h2>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-2">
              以下是一个博文生成Prompt的示例：
            </p>
            <pre className="text-xs text-gray-700 whitespace-pre-wrap">
{`请根据以下关键词生成一篇高质量的SEO博文：

关键词: {keywords}
语言: {language}
标题: {title}

请按照以下JSON格式返回结果：
{
  "title": "博文标题",
  "content": "博文正文内容",
  "excerpt": "博文摘要",
  "tags": ["标签1", "标签2"]
}`}
            </pre>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
