'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  FileText, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Filter,
  MoreHorizontal,
  Calendar,
  User,
  Tag
} from 'lucide-react'
import { PostWithRelations, POST_STATUSES } from '@/types'
import { formatDate, timeAgo } from '@/lib/utils'

export default function PostsPage() {
  const [posts, setPosts] = useState<PostWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')

  // 获取博文列表
  const fetchPosts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        project_id: 'default',
      })
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/posts?${params}`)
      const result = await response.json()
      
      if (result.success) {
        setPosts(result.data || [])
      } else {
        console.error('获取博文失败:', result.error)
      }
    } catch (error) {
      console.error('获取博文失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPosts()
  }, [statusFilter])

  // 删除博文
  const deletePost = async (id: string) => {
    if (!confirm('确定要删除这篇博文吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/posts/${id}`, {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        setPosts(posts.filter(post => post.id !== id))
        alert('博文删除成功')
      } else {
        alert('删除失败：' + result.error)
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 更新博文状态
  const updatePostStatus = async (id: string, status: string) => {
    try {
      const response = await fetch(`/api/posts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        setPosts(posts.map(post => 
          post.id === id ? { ...post, status } : post
        ))
        alert('状态更新成功')
      } else {
        alert('更新失败：' + result.error)
      }
    } catch (error) {
      console.error('更新失败:', error)
      alert('更新失败，请重试')
    }
  }

  // 过滤博文
  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || post.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'status-badge draft' },
      published: { label: '已发布', className: 'status-badge published' },
      archived: { label: '已归档', className: 'status-badge archived' },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <span className={config.className}>{config.label}</span>
  }

  return (
    <MainLayout title="博文管理" subtitle="管理您的所有博文内容">
      <div className="space-y-6">
        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            {/* 搜索 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索博文..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            {/* 筛选 */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {POST_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Link href="/generate">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              生成新博文
            </Button>
          </Link>
        </div>

        {/* 博文列表 */}
        <div className="card">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="loading-spinner"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无博文</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== 'all' ? '没有找到匹配的博文' : '开始创建您的第一篇博文'}
              </p>
              <Link href="/generate">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  生成博文
                </Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead>
                  <tr>
                    <th>标题</th>
                    <th>状态</th>
                    <th>分类</th>
                    <th>作者</th>
                    <th>创建时间</th>
                    <th>字数</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPosts.map((post) => (
                    <tr key={post.id}>
                      <td>
                        <div>
                          <div className="font-medium text-gray-900 truncate max-w-xs">
                            {post.title}
                          </div>
                          {post.excerpt && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {post.excerpt}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>{getStatusBadge(post.status)}</td>
                      <td>
                        {post.category && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                            <Tag className="h-3 w-3 mr-1" />
                            {post.category}
                          </span>
                        )}
                      </td>
                      <td>
                        {post.author ? (
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1 text-gray-400" />
                            <span className="text-sm">{post.author.name}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td>
                        <div className="text-sm">
                          <div>{formatDate(post.created_at)}</div>
                          <div className="text-gray-500">{timeAgo(post.created_at)}</div>
                        </div>
                      </td>
                      <td>
                        <span className="text-sm text-gray-600">
                          {post.word_count || 0} 字
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Link href={`/posts/${post.id}`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/posts/${post.id}/edit`}>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => deletePost(post.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 统计信息 */}
        {!loading && filteredPosts.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            共 {filteredPosts.length} 篇博文
            {searchQuery && ` · 搜索"${searchQuery}"`}
            {statusFilter !== 'all' && ` · 状态：${POST_STATUSES.find(s => s.value === statusFilter)?.label}`}
          </div>
        )}
      </div>
    </MainLayout>
  )
}
