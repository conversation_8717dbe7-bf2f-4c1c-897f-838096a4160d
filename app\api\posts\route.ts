import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'
import { CreatePostForm } from '@/types'
import { slugify, calculateReadingTime, countWords } from '@/lib/utils'

// 获取博文列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('project_id') || 'default'
    const status = searchParams.get('status')
    const seriesId = searchParams.get('series_id')
    const authorId = searchParams.get('author_id')
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined

    const filters = {
      status: status || undefined,
      seriesId: seriesId || undefined,
      authorId: authorId || undefined,
      limit,
    }

    const posts = await supabaseService.getPosts(projectId, filters)

    return NextResponse.json({
      success: true,
      data: posts,
    })
  } catch (error) {
    console.error('获取博文列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取博文列表失败' },
      { status: 500 }
    )
  }
}

// 创建新博文
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const postData: CreatePostForm & {
      project_id: string
      slug?: string
      word_count?: number
      reading_time?: number
      ai_summary?: string
      generation_params?: any
    } = body

    // 验证必需字段
    if (!postData.title || !postData.content) {
      return NextResponse.json(
        { success: false, error: '标题和内容不能为空' },
        { status: 400 }
      )
    }

    // 生成slug（如果没有提供）
    if (!postData.slug) {
      postData.slug = slugify(postData.title)
    }

    // 计算字数和阅读时间（如果没有提供）
    if (!postData.word_count) {
      postData.word_count = countWords(postData.content)
    }
    if (!postData.reading_time) {
      postData.reading_time = calculateReadingTime(postData.content)
    }

    // 设置默认项目ID
    if (!postData.project_id) {
      postData.project_id = 'default'
    }

    const post = await supabaseService.createPost({
      project_id: postData.project_id,
      series_id: postData.series_id || null,
      author_id: postData.author_id || null,
      prompt_id: postData.prompt_id || null,
      title: postData.title,
      slug: postData.slug,
      content: postData.content,
      excerpt: postData.excerpt || null,
      keywords: postData.keywords || null,
      language: postData.language || 'zh',
      meta_title: postData.meta_title || null,
      meta_description: postData.meta_description || null,
      meta_keywords: postData.meta_keywords || null,
      category: postData.category || null,
      tags: postData.tags || null,
      status: 'draft',
      generation_params: postData.generation_params || null,
      ai_summary: postData.ai_summary || null,
      word_count: postData.word_count,
      reading_time: postData.reading_time,
    })

    // 如果博文属于某个系列，更新系列的博文数量
    if (postData.series_id) {
      await updateSeriesPostCount(postData.series_id)
    }

    return NextResponse.json({
      success: true,
      data: post,
      message: '博文创建成功',
    })
  } catch (error) {
    console.error('创建博文失败:', error)
    return NextResponse.json(
      { success: false, error: '创建博文失败' },
      { status: 500 }
    )
  }
}

// 更新系列博文数量
async function updateSeriesPostCount(seriesId: string) {
  try {
    // 这里需要实现更新系列博文数量的逻辑
    // 由于我们还没有实现完整的supabase服务，这里先留空
    console.log('更新系列博文数量:', seriesId)
  } catch (error) {
    console.error('更新系列博文数量失败:', error)
  }
}
