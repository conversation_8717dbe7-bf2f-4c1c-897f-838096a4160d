'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Share2, 
  Calendar,
  User,
  Tag,
  Clock,
  FileText,
  Eye,
  Globe
} from 'lucide-react'
import { PostWithRelations } from '@/types'
import { formatDate, timeAgo } from '@/lib/utils'

export default function PostDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [post, setPost] = useState<PostWithRelations | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchPost = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/posts/${params.id}`)
      const result = await response.json()
      
      if (result.success) {
        setPost(result.data)
      } else {
        console.error('获取博文失败:', result.error)
        router.push('/posts')
      }
    } catch (error) {
      console.error('获取博文失败:', error)
      router.push('/posts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchPost()
    }
  }, [params.id])

  const deletePost = async () => {
    if (!post || !confirm('确定要删除这篇博文吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/posts/${post.id}`, {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert('博文删除成功')
        router.push('/posts')
      } else {
        alert('删除失败：' + result.error)
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  const updateStatus = async (status: string) => {
    if (!post) return

    try {
      const response = await fetch(`/api/posts/${post.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        setPost({ ...post, status })
        alert('状态更新成功')
      } else {
        alert('更新失败：' + result.error)
      }
    } catch (error) {
      console.error('更新失败:', error)
      alert('更新失败，请重试')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'status-badge draft' },
      published: { label: '已发布', className: 'status-badge published' },
      archived: { label: '已归档', className: 'status-badge archived' },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <span className={config.className}>{config.label}</span>
  }

  if (loading) {
    return (
      <MainLayout title="加载中...">
        <div className="flex justify-center items-center py-12">
          <div className="loading-spinner"></div>
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      </MainLayout>
    )
  }

  if (!post) {
    return (
      <MainLayout title="博文不存在">
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">博文不存在</h3>
          <p className="text-gray-600 mb-4">您访问的博文可能已被删除或不存在</p>
          <Link href="/posts">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回博文列表
            </Button>
          </Link>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <Link href="/posts">
            <Button variant="ghost">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回列表
            </Button>
          </Link>
          
          <div className="flex items-center space-x-2">
            {post.status === 'draft' && (
              <Button onClick={() => updateStatus('published')} variant="outline">
                <Globe className="h-4 w-4 mr-2" />
                发布
              </Button>
            )}
            {post.status === 'published' && (
              <Button onClick={() => updateStatus('draft')} variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                撤回草稿
              </Button>
            )}
            <Link href={`/posts/${post.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            </Link>
            <Button variant="outline">
              <Share2 className="h-4 w-4 mr-2" />
              分享
            </Button>
            <Button 
              variant="outline" 
              onClick={deletePost}
              className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          </div>
        </div>

        {/* 博文信息卡片 */}
        <div className="card">
          <div className="space-y-4">
            {/* 标题和状态 */}
            <div className="flex items-start justify-between">
              <h1 className="text-3xl font-bold text-gray-900 flex-1 mr-4">
                {post.title}
              </h1>
              {getStatusBadge(post.status)}
            </div>

            {/* 元信息 */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              {post.author && (
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  {post.author.name}
                </div>
              )}
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {formatDate(post.created_at)}
              </div>
              {post.word_count && (
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  {post.word_count} 字
                </div>
              )}
              {post.reading_time && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  约 {post.reading_time} 分钟阅读
                </div>
              )}
            </div>

            {/* 摘要 */}
            {post.excerpt && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">摘要</h3>
                <p className="text-gray-600">{post.excerpt}</p>
              </div>
            )}

            {/* 标签 */}
            {post.tags && Array.isArray(post.tags) && post.tags.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* SEO信息 */}
        {(post.meta_title || post.meta_description) && (
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">SEO信息</h2>
            </div>
            <div className="space-y-4">
              {post.meta_title && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SEO标题
                  </label>
                  <p className="text-gray-900">{post.meta_title}</p>
                </div>
              )}
              {post.meta_description && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SEO描述
                  </label>
                  <p className="text-gray-900">{post.meta_description}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 博文内容 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">内容</h2>
          </div>
          <div className="prose max-w-none">
            <pre className="whitespace-pre-wrap font-sans text-gray-700 leading-relaxed">
              {post.content}
            </pre>
          </div>
        </div>

        {/* 系列信息 */}
        {post.series && (
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">所属系列</h2>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{post.series.name}</h3>
              {post.series.description && (
                <p className="text-gray-600 mt-1">{post.series.description}</p>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
