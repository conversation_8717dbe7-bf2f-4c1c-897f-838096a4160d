import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'

// 获取单个项目
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // 模拟数据 - 实际项目中从数据库获取
    const mockProject = {
      id,
      name: id === 'default' ? '默认项目' : '生产环境博客',
      description: id === 'default' 
        ? '系统默认项目，用于存储初始数据和测试' 
        : '主要的生产环境博客项目，包含所有正式发布的内容',
      database_url: id === 'default' 
        ? null 
        : 'postgresql://user:password@localhost:5432/blog_prod',
      api_key: id === 'default' 
        ? null 
        : 'prod_api_key_***',
      status: 'active',
      created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: mockProject,
    })
  } catch (error) {
    console.error('获取项目失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目失败' },
      { status: 500 }
    )
  }
}

// 更新项目
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // 验证数据库URL格式（如果提供）
    if (body.database_url) {
      try {
        new URL(body.database_url)
      } catch {
        return NextResponse.json(
          { success: false, error: '数据库URL格式不正确' },
          { status: 400 }
        )
      }
    }

    console.log('更新项目:', id, body)

    return NextResponse.json({
      success: true,
      message: '项目更新成功',
    })
  } catch (error) {
    console.error('更新项目失败:', error)
    return NextResponse.json(
      { success: false, error: '更新项目失败' },
      { status: 500 }
    )
  }
}

// 删除项目
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // 防止删除默认项目
    if (id === 'default') {
      return NextResponse.json(
        { success: false, error: '不能删除默认项目' },
        { status: 400 }
      )
    }

    console.log('删除项目:', id)

    return NextResponse.json({
      success: true,
      message: '项目删除成功',
    })
  } catch (error) {
    console.error('删除项目失败:', error)
    return NextResponse.json(
      { success: false, error: '删除项目失败' },
      { status: 500 }
    )
  }
}
