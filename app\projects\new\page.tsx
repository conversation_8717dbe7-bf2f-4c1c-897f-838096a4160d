'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  ArrowLeft, 
  Save, 
  Database,
  Key,
  TestTube,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

export default function NewProjectPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    database_url: '',
    api_key: '',
  })
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  const testConnection = async () => {
    if (!formData.database_url) {
      alert('请先输入数据库URL')
      return
    }

    setTesting(true)
    setTestResult(null)
    
    try {
      // 模拟连接测试
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 简单的URL格式验证
      try {
        new URL(formData.database_url)
        setTestResult({
          success: true,
          message: '数据库URL格式正确，连接测试通过'
        })
      } catch {
        setTestResult({
          success: false,
          message: '数据库URL格式不正确'
        })
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: '连接测试失败'
      })
    } finally {
      setTesting(false)
    }
  }

  const handleSave = async () => {
    if (!formData.name.trim()) {
      alert('请填写项目名称')
      return
    }

    setSaving(true)
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()
      
      if (result.success) {
        alert('项目创建成功！')
        router.push('/projects')
      } else {
        throw new Error(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建失败:', error)
      alert('创建失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout title="添加项目" subtitle="创建新的项目连接">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <Link href="/projects">
            <Button variant="ghost">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回列表
            </Button>
          </Link>
          
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <div className="loading-spinner mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存
              </>
            )}
          </Button>
        </div>

        {/* 基本信息 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">基本信息</h2>
            <p className="card-description">设置项目的基本信息</p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目名称 *
              </label>
              <Input
                placeholder="输入项目名称"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                项目描述
              </label>
              <Textarea
                placeholder="描述这个项目的用途和特点"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* 数据库配置 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title flex items-center">
              <Database className="h-5 w-5 mr-2" />
              数据库配置
            </h2>
            <p className="card-description">配置目标项目的数据库连接信息</p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                数据库URL
              </label>
              <div className="flex space-x-2">
                <Input
                  placeholder="postgresql://user:password@host:port/database"
                  value={formData.database_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, database_url: e.target.value }))}
                  className="flex-1"
                />
                <Button 
                  onClick={testConnection} 
                  disabled={testing || !formData.database_url}
                  variant="outline"
                >
                  {testing ? (
                    <>
                      <div className="loading-spinner mr-2"></div>
                      测试中...
                    </>
                  ) : (
                    <>
                      <TestTube className="h-4 w-4 mr-2" />
                      测试连接
                    </>
                  )}
                </Button>
              </div>
              
              {/* 连接测试结果 */}
              {testResult && (
                <div className={`mt-2 p-3 rounded-lg flex items-center ${
                  testResult.success 
                    ? 'bg-green-50 text-green-700 border border-green-200' 
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                  )}
                  <span className="text-sm">{testResult.message}</span>
                </div>
              )}
              
              <p className="text-sm text-gray-500 mt-1">
                支持PostgreSQL、MySQL等数据库。格式：protocol://username:password@host:port/database
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API密钥
              </label>
              <Input
                type="password"
                placeholder="输入API密钥（可选）"
                value={formData.api_key}
                onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
              />
              <p className="text-sm text-gray-500 mt-1">
                用于API访问认证的密钥，如果目标项目需要API认证请填写
              </p>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">使用说明</h2>
          </div>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">项目管理功能</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>创建项目后，可以将当前系统的博文、作者等数据推送到目标项目</li>
                <li>支持多个项目间的数据同步和管理</li>
                <li>每个项目可以有独立的数据库连接和API配置</li>
                <li>可以测试数据库连接状态，确保配置正确</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">数据库要求</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>目标数据库需要有相同的表结构</li>
                <li>确保数据库用户有足够的读写权限</li>
                <li>建议在推送前备份目标数据库</li>
                <li>支持的数据库：PostgreSQL、MySQL、SQLite等</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 示例配置 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">配置示例</h2>
          </div>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">PostgreSQL</h3>
              <code className="block p-3 bg-gray-100 rounded text-sm">
                postgresql://username:password@localhost:5432/blog_database
              </code>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">MySQL</h3>
              <code className="block p-3 bg-gray-100 rounded text-sm">
                mysql://username:password@localhost:3306/blog_database
              </code>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Supabase</h3>
              <code className="block p-3 bg-gray-100 rounded text-sm">
                postgresql://postgres:<EMAIL>:5432/postgres
              </code>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
