'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  FolderOpen, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Database,
  Key,
  Upload,
  Download,
  Settings,
  CheckCircle,
  XCircle,
  Calendar
} from 'lucide-react'
import { Project, PROJECT_STATUSES } from '@/types'
import { formatDate, timeAgo } from '@/lib/utils'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/projects?${params}`)
      const result = await response.json()
      
      if (result.success) {
        setProjects(result.data || [])
      } else {
        console.error('获取项目失败:', result.error)
      }
    } catch (error) {
      console.error('获取项目失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [statusFilter])

  // 删除项目
  const deleteProject = async (id: string) => {
    if (!confirm('确定要删除这个项目吗？这将删除项目下的所有数据！')) {
      return
    }

    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        setProjects(projects.filter(project => project.id !== id))
        alert('项目删除成功')
      } else {
        alert('删除失败：' + result.error)
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 测试数据库连接
  const testConnection = async (project: Project) => {
    try {
      const response = await fetch(`/api/projects/${project.id}/test-connection`, {
        method: 'POST',
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert('数据库连接测试成功！')
      } else {
        alert('连接测试失败：' + result.error)
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      alert('连接测试失败，请重试')
    }
  }

  // 推送数据到项目
  const pushDataToProject = async (project: Project) => {
    if (!confirm(`确定要将当前数据推送到项目"${project.name}"吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/projects/${project.id}/push-data`, {
        method: 'POST',
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert('数据推送成功！')
      } else {
        alert('推送失败：' + result.error)
      }
    } catch (error) {
      console.error('推送失败:', error)
      alert('推送失败，请重试')
    }
  }

  // 过滤项目
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description?.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesSearch
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '活跃', className: 'status-badge active', icon: CheckCircle },
      inactive: { label: '非活跃', className: 'status-badge inactive', icon: XCircle },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active
    const Icon = config.icon
    
    return (
      <span className={`${config.className} flex items-center`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </span>
    )
  }

  return (
    <MainLayout title="项目管理" subtitle="管理您的项目和数据库连接">
      <div className="space-y-6">
        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            {/* 搜索 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索项目..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            {/* 筛选 */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {PROJECT_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Link href="/projects/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加项目
            </Button>
          </Link>
        </div>

        {/* 项目列表 */}
        <div className="card">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="loading-spinner"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <FolderOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== 'all' 
                  ? '没有找到匹配的项目' 
                  : '开始添加您的第一个项目'}
              </p>
              <Link href="/projects/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  添加项目
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredProjects.map((project) => (
                <div key={project.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  {/* 头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <FolderOpen className="h-5 w-5 text-blue-600" />
                        <h3 className="text-lg font-medium text-gray-900">
                          {project.name}
                        </h3>
                        {getStatusBadge(project.status)}
                      </div>
                      {project.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {project.description}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 连接信息 */}
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Database className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span className="truncate">
                        {project.database_url ? '已配置数据库' : '未配置数据库'}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Key className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span className="truncate">
                        {project.api_key ? '已配置API密钥' : '未配置API密钥'}
                      </span>
                    </div>
                  </div>

                  {/* 元信息 */}
                  <div className="text-xs text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      创建于 {formatDate(project.created_at)}
                    </div>
                    <div className="mt-1">
                      更新于 {timeAgo(project.updated_at)}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Link href={`/projects/${project.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/projects/${project.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => testConnection(project)}
                        disabled={!project.database_url}
                        title="测试数据库连接"
                      >
                        <Database className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => pushDataToProject(project)}
                        disabled={!project.database_url}
                        className="text-green-600 hover:text-green-700"
                        title="推送数据到此项目"
                      >
                        <Upload className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => deleteProject(project.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 统计信息 */}
        {!loading && filteredProjects.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            共 {filteredProjects.length} 个项目
            {searchQuery && ` · 搜索"${searchQuery}"`}
            {statusFilter !== 'all' && ` · 状态：${PROJECT_STATUSES.find(s => s.value === statusFilter)?.label}`}
          </div>
        )}

        {/* 使用说明 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">项目管理说明</h2>
          </div>
          <div className="space-y-4 text-sm text-gray-600">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">功能说明</h3>
                <ul className="space-y-1">
                  <li>• 管理多个项目的数据库连接</li>
                  <li>• 测试数据库连接状态</li>
                  <li>• 推送博文和作者数据到指定项目</li>
                  <li>• 支持不同项目间的数据同步</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">操作指南</h3>
                <ul className="space-y-1">
                  <li>• 点击 <Database className="inline h-3 w-3" /> 测试数据库连接</li>
                  <li>• 点击 <Upload className="inline h-3 w-3" /> 推送数据到项目</li>
                  <li>• 点击 <Edit className="inline h-3 w-3" /> 编辑项目配置</li>
                  <li>• 确保配置正确的数据库URL和API密钥</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
