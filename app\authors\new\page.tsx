'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  ArrowLeft, 
  Save, 
  Plus,
  X,
  Upload,
  User
} from 'lucide-react'

export default function NewAuthorPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    bio: '',
    avatar_url: '',
    email: '',
    website: '',
    social_links: {} as Record<string, string>,
    expertise: [] as string[],
  })
  const [saving, setSaving] = useState(false)
  const [newExpertise, setNewExpertise] = useState('')
  const [socialLinks, setSocialLinks] = useState([
    { platform: 'twitter', url: '' },
    { platform: 'github', url: '' },
    { platform: 'linkedin', url: '' },
    { platform: 'weibo', url: '' },
  ])

  const addExpertise = () => {
    if (newExpertise.trim() && !formData.expertise.includes(newExpertise.trim())) {
      setFormData(prev => ({
        ...prev,
        expertise: [...prev.expertise, newExpertise.trim()]
      }))
      setNewExpertise('')
    }
  }

  const removeExpertise = (expertise: string) => {
    setFormData(prev => ({
      ...prev,
      expertise: prev.expertise.filter(e => e !== expertise)
    }))
  }

  const handleExpertiseKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addExpertise()
    }
  }

  const handleSocialLinkChange = (platform: string, url: string) => {
    setSocialLinks(prev => 
      prev.map(link => 
        link.platform === platform ? { ...link, url } : link
      )
    )
    
    // 更新formData中的social_links
    const socialLinksObj = socialLinks.reduce((acc, link) => {
      if (link.url.trim()) {
        acc[link.platform] = link.url.trim()
      }
      return acc
    }, {} as Record<string, string>)
    
    if (url.trim()) {
      socialLinksObj[platform] = url.trim()
    }
    
    setFormData(prev => ({
      ...prev,
      social_links: socialLinksObj
    }))
  }

  const handleSave = async () => {
    if (!formData.name.trim()) {
      alert('请填写作者姓名')
      return
    }

    setSaving(true)
    try {
      const response = await fetch('/api/authors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          project_id: 'default',
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        alert('作者创建成功！')
        router.push('/authors')
      } else {
        throw new Error(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建失败:', error)
      alert('创建失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout title="添加作者" subtitle="创建新的作者信息">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 头部操作栏 */}
        <div className="flex items-center justify-between">
          <Link href="/authors">
            <Button variant="ghost">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回列表
            </Button>
          </Link>
          
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <div className="loading-spinner mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存
              </>
            )}
          </Button>
        </div>

        {/* 基本信息 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">基本信息</h2>
            <p className="card-description">设置作者的基本信息</p>
          </div>

          <div className="space-y-6">
            {/* 头像 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                头像
              </label>
              <div className="flex items-center space-x-4">
                <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
                  {formData.avatar_url ? (
                    <img
                      src={formData.avatar_url}
                      alt="头像预览"
                      className="h-20 w-20 rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <Input
                    placeholder="输入头像URL"
                    value={formData.avatar_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, avatar_url: e.target.value }))}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    输入图片URL或点击上传按钮
                  </p>
                </div>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  上传
                </Button>
              </div>
            </div>

            {/* 姓名和邮箱 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <Input
                  placeholder="输入作者姓名"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱
                </label>
                <Input
                  type="email"
                  placeholder="输入邮箱地址"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
            </div>

            {/* 个人简介 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                个人简介
              </label>
              <Textarea
                placeholder="介绍作者的背景、经验和专业领域..."
                value={formData.bio}
                onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                rows={4}
              />
            </div>

            {/* 个人网站 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                个人网站
              </label>
              <Input
                type="url"
                placeholder="https://example.com"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
              />
            </div>
          </div>
        </div>

        {/* 专业领域 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">专业领域</h2>
            <p className="card-description">添加作者的专业技能和领域</p>
          </div>

          <div className="space-y-4">
            {/* 添加专业领域 */}
            <div className="flex space-x-2">
              <Input
                placeholder="输入专业领域后按回车添加"
                value={newExpertise}
                onChange={(e) => setNewExpertise(e.target.value)}
                onKeyPress={handleExpertiseKeyPress}
                className="flex-1"
              />
              <Button onClick={addExpertise} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* 专业领域列表 */}
            <div className="flex flex-wrap gap-2">
              {formData.expertise.map((expertise) => (
                <span
                  key={expertise}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                >
                  {expertise}
                  <button
                    onClick={() => removeExpertise(expertise)}
                    className="ml-2 hover:text-blue-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* 社交媒体 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">社交媒体</h2>
            <p className="card-description">添加作者的社交媒体链接</p>
          </div>

          <div className="space-y-4">
            {socialLinks.map((link) => (
              <div key={link.platform} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                <label className="text-sm font-medium text-gray-700 capitalize">
                  {link.platform}
                </label>
                <div className="md:col-span-3">
                  <Input
                    placeholder={`输入${link.platform}链接`}
                    value={link.url}
                    onChange={(e) => handleSocialLinkChange(link.platform, e.target.value)}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 预览 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">预览</h2>
          </div>
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                {formData.avatar_url ? (
                  <img
                    src={formData.avatar_url}
                    alt={formData.name}
                    className="h-16 w-16 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-medium text-gray-900">
                  {formData.name || '作者姓名'}
                </h3>
                {formData.bio && (
                  <p className="text-gray-600 mt-2">{formData.bio}</p>
                )}
                {formData.expertise.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {formData.expertise.map((expertise) => (
                      <span
                        key={expertise}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                      >
                        {expertise}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
