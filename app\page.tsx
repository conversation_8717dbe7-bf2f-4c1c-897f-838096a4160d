'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { 
  PenTool, 
  FileText, 
  MessageSquare, 
  User,
  TrendingUp,
  Clock,
  Target
} from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  const stats = [
    {
      name: '总博文数',
      value: '0',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: '已发布',
      value: '0',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: '草稿',
      value: '0',
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      name: '活跃作者',
      value: '0',
      icon: User,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ]

  const quickActions = [
    {
      name: '生成新博文',
      description: '使用AI快速生成高质量博文',
      href: '/generate',
      icon: PenTool,
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      name: '管理博文',
      description: '查看和编辑已有博文',
      href: '/posts',
      icon: FileText,
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      name: '管理Prompt',
      description: '创建和编辑AI生成模板',
      href: '/prompts',
      icon: MessageSquare,
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      name: '管理作者',
      description: '添加和管理作者信息',
      href: '/authors',
      icon: User,
      color: 'bg-orange-600 hover:bg-orange-700',
    },
  ]

  return (
    <MainLayout title="仪表板" subtitle="欢迎使用Auto Blog SaaS系统">
      <div className="space-y-8 page-transition">
        {/* 欢迎横幅 */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-strong">
          <div className="relative z-10">
            <h1 className="text-3xl font-bold mb-2">欢迎使用 Auto Blog SaaS</h1>
            <p className="text-blue-100 text-lg">使用AI技术快速生成高质量SEO博文，提升您的内容创作效率</p>
          </div>
          <div className="absolute top-0 right-0 -mt-4 -mr-4 h-32 w-32 rounded-full bg-white/10"></div>
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-24 w-24 rounded-full bg-white/5"></div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={stat.name} className="card animate-bounce-in" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="flex items-center">
                <div className={`p-4 rounded-xl ${stat.bgColor} shadow-soft`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 快速操作 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">快速操作</h2>
            <p className="card-description">选择一个操作开始使用系统</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <Link key={action.name} href={action.href}>
                <div className="group p-6 rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-medium transition-all duration-300 cursor-pointer bg-gradient-to-br from-white to-gray-50 hover:from-gray-50 hover:to-white animate-slide-in" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className={`inline-flex p-4 rounded-xl text-white ${action.color} group-hover:scale-110 transition-all duration-300 shadow-soft`}>
                    <action.icon className="h-6 w-6" />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {action.name}
                  </h3>
                  <p className="mt-2 text-sm text-gray-600 leading-relaxed">
                    {action.description}
                  </p>
                  <div className="mt-4 text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                    开始使用 →
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* 最近活动 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最近博文 */}
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">最近博文</h2>
              <Link href="/posts">
                <Button variant="outline" size="sm">查看全部</Button>
              </Link>
            </div>
            <div className="space-y-4">
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无博文</p>
                <Link href="/generate">
                  <Button className="mt-4">
                    <PenTool className="h-4 w-4 mr-2" />
                    生成第一篇博文
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* 系统状态 */}
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">系统状态</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">数据库连接</span>
                <span className="status-badge active">正常</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">AI服务</span>
                <span className="status-badge active">就绪</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">存储空间</span>
                <span className="text-sm text-gray-900">充足</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">API调用</span>
                <span className="text-sm text-gray-900">0 / 1000</span>
              </div>
            </div>
          </div>
        </div>

        {/* 使用指南 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">快速开始</h2>
            <p className="card-description">按照以下步骤开始使用Auto Blog系统</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                <span className="text-xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">配置作者信息</h3>
              <p className="text-sm text-gray-600">添加作者信息，包括姓名、简介等基本信息</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                <span className="text-xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">创建Prompt模板</h3>
              <p className="text-sm text-gray-600">设置AI生成博文的提示词模板</p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                <span className="text-xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">生成博文</h3>
              <p className="text-sm text-gray-600">输入关键词，让AI为您生成高质量博文</p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
