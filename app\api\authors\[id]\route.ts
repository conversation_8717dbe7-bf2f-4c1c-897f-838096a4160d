import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'

// 获取单个作者
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // 模拟数据 - 实际项目中从数据库获取
    const mockAuthor = {
      id,
      project_id: 'default',
      name: '张三',
      bio: '资深技术博主，专注于前端开发和AI技术。拥有10年以上的开发经验，热衷于分享技术知识和最佳实践。曾在多家知名互联网公司担任技术负责人，对技术架构和团队管理有深入的理解。',
      avatar_url: null,
      email: '<EMAIL>',
      website: 'https://zhangsan.dev',
      social_links: {
        twitter: '@zhangsan',
        github: 'zhangsan',
        linkedin: 'zhangsan',
        weibo: '@张三技术博客'
      },
      expertise: ['JavaScript', 'React', 'Node.js', 'AI', '前端开发', 'TypeScript', 'Vue.js', '微服务架构'],
      status: 'active',
      created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: mockAuthor,
    })
  } catch (error) {
    console.error('获取作者失败:', error)
    return NextResponse.json(
      { success: false, error: '获取作者失败' },
      { status: 500 }
    )
  }
}

// 更新作者
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    console.log('更新作者:', id, body)

    return NextResponse.json({
      success: true,
      message: '作者更新成功',
    })
  } catch (error) {
    console.error('更新作者失败:', error)
    return NextResponse.json(
      { success: false, error: '更新作者失败' },
      { status: 500 }
    )
  }
}

// 删除作者
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    console.log('删除作者:', id)

    return NextResponse.json({
      success: true,
      message: '作者删除成功',
    })
  } catch (error) {
    console.error('删除作者失败:', error)
    return NextResponse.json(
      { success: false, error: '删除作者失败' },
      { status: 500 }
    )
  }
}
