import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'
import { CreateAuthorForm } from '@/types'

// 获取作者列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('project_id') || 'default'
    const status = searchParams.get('status')

    // 模拟数据 - 实际项目中从数据库获取
    const mockAuthors = [
      {
        id: '1',
        project_id: projectId,
        name: '张三',
        bio: '资深技术博主，专注于前端开发和AI技术。拥有10年以上的开发经验，热衷于分享技术知识和最佳实践。',
        avatar_url: null,
        email: '<EMAIL>',
        website: 'https://zhangsan.dev',
        social_links: {
          twitter: '@zhangsan',
          github: 'zhangsan',
          linkedin: 'zhangsan'
        },
        expertise: ['JavaScript', 'React', 'Node.js', 'AI', '前端开发'],
        status: 'active',
        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '2',
        project_id: projectId,
        name: '李四',
        bio: '全栈开发工程师，专注于Web开发和云计算。喜欢探索新技术，并将其应用到实际项目中。',
        avatar_url: null,
        email: '<EMAIL>',
        website: 'https://lisi.blog',
        social_links: {
          twitter: '@lisi',
          github: 'lisi'
        },
        expertise: ['Python', 'Django', 'AWS', '云计算', '后端开发'],
        status: 'active',
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '3',
        project_id: projectId,
        name: '王五',
        bio: '数据科学家和机器学习专家，致力于用数据驱动决策。在多个行业有丰富的数据分析经验。',
        avatar_url: null,
        email: '<EMAIL>',
        website: null,
        social_links: {
          linkedin: 'wangwu'
        },
        expertise: ['Python', '机器学习', '数据分析', 'TensorFlow', '数据科学'],
        status: 'inactive',
        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      }
    ]

    // 应用筛选
    let filteredAuthors = mockAuthors
    if (status && status !== 'all') {
      filteredAuthors = filteredAuthors.filter(a => a.status === status)
    }

    return NextResponse.json({
      success: true,
      data: filteredAuthors,
    })
  } catch (error) {
    console.error('获取作者列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取作者列表失败' },
      { status: 500 }
    )
  }
}

// 创建新作者
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const authorData: CreateAuthorForm & {
      project_id: string
    } = body

    // 验证必需字段
    if (!authorData.name) {
      return NextResponse.json(
        { success: false, error: '作者姓名不能为空' },
        { status: 400 }
      )
    }

    // 设置默认值
    if (!authorData.project_id) {
      authorData.project_id = 'default'
    }

    // 模拟创建作者
    const newAuthor = {
      id: Date.now().toString(),
      ...authorData,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('创建作者:', newAuthor)

    return NextResponse.json({
      success: true,
      data: newAuthor,
      message: '作者创建成功',
    })
  } catch (error) {
    console.error('创建作者失败:', error)
    return NextResponse.json(
      { success: false, error: '创建作者失败' },
      { status: 500 }
    )
  }
}
