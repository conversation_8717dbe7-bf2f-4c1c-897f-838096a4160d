import { AIProvider, AIGenerationRequest, AIGenerationResponse, PostGenerationParams } from '@/types'

// AI 服务基类
abstract class AIService {
  abstract generateContent(request: AIGenerationRequest): Promise<AIGenerationResponse>
}

// OpenAI 服务
class OpenAIService extends AIService {
  private apiKey: string

  constructor(apiKey: string) {
    super()
    this.apiKey = apiKey
  }

  async generateContent(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: request.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: request.prompt,
          },
        ],
        temperature: request.temperature || 0.7,
        max_tokens: request.max_tokens || 2000,
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`)
    }

    const data = await response.json()
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
    }
  }
}

// Anthropic 服务
class AnthropicService extends AIService {
  private apiKey: string

  constructor(apiKey: string) {
    super()
    this.apiKey = apiKey
  }

  async generateContent(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: request.model || 'claude-3-sonnet-20240229',
        max_tokens: request.max_tokens || 2000,
        temperature: request.temperature || 0.7,
        messages: [
          {
            role: 'user',
            content: request.prompt,
          },
        ],
      }),
    })

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`)
    }

    const data = await response.json()
    return {
      content: data.content[0]?.text || '',
      usage: data.usage,
    }
  }
}

// Google AI 服务
class GoogleAIService extends AIService {
  private apiKey: string

  constructor(apiKey: string) {
    super()
    this.apiKey = apiKey
  }

  async generateContent(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/${request.model || 'gemini-pro'}:generateContent?key=${this.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: request.prompt,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: request.temperature || 0.7,
            maxOutputTokens: request.max_tokens || 2000,
          },
        }),
      }
    )

    if (!response.ok) {
      throw new Error(`Google AI API error: ${response.statusText}`)
    }

    const data = await response.json()
    return {
      content: data.candidates[0]?.content?.parts[0]?.text || '',
    }
  }
}

// AI 管理器
class AIManager {
  private services: Map<AIProvider, AIService> = new Map()

  constructor() {
    // 初始化服务
    if (process.env.OPENAI_API_KEY) {
      this.services.set('openai', new OpenAIService(process.env.OPENAI_API_KEY))
    }
    if (process.env.ANTHROPIC_API_KEY) {
      this.services.set('anthropic', new AnthropicService(process.env.ANTHROPIC_API_KEY))
    }
    if (process.env.GOOGLE_AI_API_KEY) {
      this.services.set('google', new GoogleAIService(process.env.GOOGLE_AI_API_KEY))
    }
  }

  async generateContent(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const provider = request.provider || 'openai'
    const service = this.services.get(provider)

    if (!service) {
      throw new Error(`AI provider ${provider} is not configured`)
    }

    return service.generateContent(request)
  }

  // 生成博文内容
  async generateBlogPost(params: PostGenerationParams, customPrompt?: string): Promise<{
    title: string
    content: string
    excerpt: string
    meta_title: string
    meta_description: string
    tags: string[]
  }> {
    const prompt = customPrompt || this.buildBlogPostPrompt(params)
    
    const response = await this.generateContent({
      prompt,
      provider: 'openai',
      temperature: 0.7,
      max_tokens: 3000,
    })

    // 解析AI响应（这里需要根据实际AI返回格式调整）
    return this.parseBlogPostResponse(response.content)
  }

  // 生成标题
  async generateTitle(keywords: string[], language: string = 'zh'): Promise<string[]> {
    const prompt = this.buildTitlePrompt(keywords, language)
    
    const response = await this.generateContent({
      prompt,
      provider: 'openai',
      temperature: 0.8,
      max_tokens: 200,
    })

    return this.parseTitleResponse(response.content)
  }

  // 生成SEO信息
  async generateSEO(title: string, content: string, language: string = 'zh'): Promise<{
    meta_title: string
    meta_description: string
    meta_keywords: string
  }> {
    const prompt = this.buildSEOPrompt(title, content, language)
    
    const response = await this.generateContent({
      prompt,
      provider: 'openai',
      temperature: 0.5,
      max_tokens: 300,
    })

    return this.parseSEOResponse(response.content)
  }

  // 生成标签
  async generateTags(content: string, language: string = 'zh'): Promise<string[]> {
    const prompt = this.buildTagsPrompt(content, language)
    
    const response = await this.generateContent({
      prompt,
      provider: 'openai',
      temperature: 0.6,
      max_tokens: 150,
    })

    return this.parseTagsResponse(response.content)
  }

  // 生成系列总结
  async generateSeriesSummary(posts: Array<{ title: string; content: string; ai_summary?: string }>): Promise<string> {
    const prompt = this.buildSeriesSummaryPrompt(posts)
    
    const response = await this.generateContent({
      prompt,
      provider: 'openai',
      temperature: 0.5,
      max_tokens: 500,
    })

    return response.content.trim()
  }

  // 构建博文生成提示词
  private buildBlogPostPrompt(params: PostGenerationParams): string {
    const keywordsStr = params.keywords.join(', ')
    const languageMap: Record<string, string> = {
      zh: '中文',
      en: 'English',
      ja: '日本語',
      ko: '한국어',
    }
    const languageName = languageMap[params.language] || '中文'

    return `请根据以下关键词生成一篇高质量的SEO博文：

关键词: ${keywordsStr}
语言: ${languageName}
${params.title ? `标题: ${params.title}` : ''}
${params.custom_instructions ? `额外要求: ${params.custom_instructions}` : ''}

请按照以下JSON格式返回结果：
{
  "title": "博文标题",
  "content": "博文正文内容（使用Markdown格式）",
  "excerpt": "博文摘要（100-150字）",
  "meta_title": "SEO标题（50-60字符）",
  "meta_description": "SEO描述（150-160字符）",
  "tags": ["标签1", "标签2", "标签3"]
}

要求：
1. 内容要原创、有价值、结构清晰
2. 自然融入关键词，避免关键词堆砌
3. 标题要吸引人且包含主要关键词
4. 内容长度在800-1500字之间
5. 使用适当的标题层级（H2、H3等）
6. 包含实用的信息和见解`
  }

    // 其他私有方法...
  private buildTitlePrompt(keywords: string[], language: string): string {
    return `请为以下关键词生成5个吸引人的博文标题：${keywords.join(', ')}\n语言：${language}\n请返回JSON数组格式：["标题1", "标题2", "标题3", "标题4", "标题5"]`
  }

  private buildSEOPrompt(title: string, content: string, language: string): string {
    return `请为以下博文生成SEO信息：\n标题：${title}\n内容：${content.substring(0, 500)}...\n语言：${language}\n\n请返回JSON格式：{"meta_title": "SEO标题", "meta_description": "SEO描述", "meta_keywords": "关键词"}`
  }

  private buildTagsPrompt(content: string, language: string): string {
    return `请为以下内容生成5-8个相关标签：\n${content.substring(0, 500)}...\n语言：${language}\n请返回JSON数组格式：["标签1", "标签2", "标签3"]`
  }

  private buildSeriesSummaryPrompt(posts: Array<{ title: string; content: string; ai_summary?: string }>): string {
    const postsInfo = posts.map((post, index) => 
      `第${index + 1}篇：${post.title}\n${post.ai_summary || post.content.substring(0, 200)}...`
    ).join('\n\n')

    return `请为以下博文系列生成一个总结，用于指导后续博文的生成：\n\n${postsInfo}\n\n请生成一个简洁的系列总结，包括主要主题、已覆盖的内容要点、以及可能的后续方向。`
  }

  // 解析AI响应的私有方法
  private parseBlogPostResponse(response: string): any {
    try {
      return JSON.parse(response)
    } catch {
      // 如果JSON解析失败，尝试提取内容
      return {
        title: '生成的博文标题',
        content: response,
        excerpt: response.substring(0, 150) + '...',
        meta_title: '生成的博文标题',
        meta_description: response.substring(0, 160) + '...',
        tags: ['AI生成', '博文'],
      }
    }
  }

  private parseTitleResponse(response: string): string[] {
    try {
      return JSON.parse(response)
    } catch {
      return response.split('\n').filter(line => line.trim()).slice(0, 5)
    }
  }

  private parseSEOResponse(response: string): any {
    try {
      return JSON.parse(response)
    } catch {
      return {
        meta_title: '默认SEO标题',
        meta_description: '默认SEO描述',
        meta_keywords: '默认关键词',
      }
    }
  }

  private parseTagsResponse(response: string): string[] {
    try {
      return JSON.parse(response)
    } catch {
      return response.split('\n').filter(line => line.trim()).slice(0, 8)
    }
  }
}

// 导出单例实例
export const aiManager = new AIManager()
