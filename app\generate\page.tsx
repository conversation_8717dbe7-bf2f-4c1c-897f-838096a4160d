'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  PenTool, 
  Wand2, 
  Save, 
  Eye,
  Plus,
  X,
  Loader2
} from 'lucide-react'
import { SUPPORTED_LANGUAGES, POST_CATEGORIES } from '@/types'

export default function GeneratePage() {
  const [formData, setFormData] = useState({
    keywords: [] as string[],
    title: '',
    language: 'zh',
    series_id: '',
    author_id: '',
    prompt_id: '',
    category: '',
    auto_generate_title: true,
    auto_generate_seo: true,
    auto_generate_tags: true,
    custom_instructions: '',
  })

  const [keywordInput, setKeywordInput] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState({
    title: '',
    content: '',
    excerpt: '',
    meta_title: '',
    meta_description: '',
    tags: [] as string[],
  })

  // 模拟数据 - 实际项目中从API获取
  const [authors, setAuthors] = useState([])
  const [prompts, setPrompts] = useState([])
  const [series, setSeries] = useState([])

  const addKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, keywordInput.trim()]
      }))
      setKeywordInput('')
    }
  }

  const removeKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }))
  }

  const handleKeywordInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addKeyword()
    }
  }

  const generateContent = async () => {
    if (formData.keywords.length === 0) {
      alert('请至少添加一个关键词')
      return
    }

    setIsGenerating(true)
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        setGeneratedContent(result.data)
      } else {
        throw new Error(result.error || '生成失败')
      }
    } catch (error) {
      console.error('生成失败:', error)
      alert('生成失败，请重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const savePost = async () => {
    if (!generatedContent.title || !generatedContent.content) {
      alert('请先生成内容')
      return
    }

    try {
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...generatedContent,
          ...formData,
          project_id: 'default', // 暂时使用默认项目
        }),
      })

      const result = await response.json()

      if (result.success) {
        alert('博文保存成功！')
        // 可以选择重定向到博文管理页面
        // router.push('/posts')
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      alert('保存失败，请重试')
    }
  }

  return (
    <MainLayout title="博文生成" subtitle="使用AI快速生成高质量SEO博文">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 生成配置 */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title flex items-center">
              <PenTool className="h-5 w-5 mr-2" />
              生成配置
            </h2>
            <p className="card-description">设置博文生成参数</p>
          </div>

          <div className="space-y-6">
            {/* 关键词输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                关键词 *
              </label>
              <div className="flex space-x-2 mb-3">
                <Input
                  placeholder="输入关键词后按回车添加"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={handleKeywordInputKeyPress}
                  className="flex-1"
                />
                <Button onClick={addKeyword} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.keywords.map((keyword) => (
                  <span
                    key={keyword}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {keyword}
                    <button
                      onClick={() => removeKeyword(keyword)}
                      className="ml-2 hover:text-blue-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* 基础设置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语言
                </label>
                <Select value={formData.language} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, language: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  分类
                </label>
                <Select value={formData.category} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, category: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {POST_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  作者
                </label>
                <Select value={formData.author_id} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, author_id: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择作者" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">默认作者</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 可选标题 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                自定义标题（可选）
              </label>
              <Input
                placeholder="留空则自动生成标题"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            {/* 自定义指令 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                自定义指令（可选）
              </label>
              <Textarea
                placeholder="添加特殊要求或指令..."
                value={formData.custom_instructions}
                onChange={(e) => setFormData(prev => ({ ...prev, custom_instructions: e.target.value }))}
                rows={3}
              />
            </div>

            {/* 生成按钮 */}
            <div className="flex justify-center">
              <Button 
                onClick={generateContent} 
                disabled={isGenerating || formData.keywords.length === 0}
                size="lg"
                className="px-8"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-5 w-5 mr-2" />
                    生成博文
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 生成结果 */}
        {generatedContent.title && (
          <div className="card">
            <div className="card-header">
              <h2 className="card-title flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                生成结果
              </h2>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  预览
                </Button>
                <Button onClick={savePost} size="sm">
                  <Save className="h-4 w-4 mr-2" />
                  保存博文
                </Button>
              </div>
            </div>

            <div className="space-y-6">
              {/* 标题和SEO信息 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">基本信息</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        标题
                      </label>
                      <Input value={generatedContent.title} readOnly />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        摘要
                      </label>
                      <Textarea value={generatedContent.excerpt} readOnly rows={3} />
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">SEO信息</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SEO标题
                      </label>
                      <Input value={generatedContent.meta_title} readOnly />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SEO描述
                      </label>
                      <Textarea value={generatedContent.meta_description} readOnly rows={3} />
                    </div>
                  </div>
                </div>
              </div>

              {/* 标签 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {generatedContent.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* 内容预览 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">内容预览</h3>
                <div className="bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto">
                  <div className="prose max-w-none">
                    <pre className="whitespace-pre-wrap font-sans text-sm text-gray-700">
                      {generatedContent.content}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
