'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  PenTool, 
  FileText, 
  MessageSquare, 
  User, 
  Settings,
  FolderOpen,
  Home
} from 'lucide-react'

const navigation = [
  {
    name: '首页',
    href: '/',
    icon: Home,
  },
  {
    name: '博文生成',
    href: '/generate',
    icon: PenTool,
  },
  {
    name: '博文管理',
    href: '/posts',
    icon: FileText,
  },
  {
    name: 'Prompt管理',
    href: '/prompts',
    icon: MessageSquare,
  },
  {
    name: '作者管理',
    href: '/authors',
    icon: User,
  },
  {
    name: '项目管理',
    href: '/projects',
    icon: FolderOpen,
  },
  {
    name: '设置',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex h-full w-64 flex-col bg-gradient-to-b from-slate-50 to-white border-r border-gray-100 shadow-soft">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-medium">
            <PenTool className="h-5 w-5 text-white" />
          </div>
          <div>
            <span className="text-xl font-bold gradient-text">Auto Blog</span>
            <div className="text-xs text-gray-500">AI博文生成系统</div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href ||
            (item.href !== '/' && pathname.startsWith(item.href))

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group relative overflow-hidden',
                isActive
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-medium transform scale-105'
                  : 'text-gray-700 hover:bg-white hover:text-gray-900 hover:shadow-soft hover:scale-105'
              )}
            >
              <item.icon className={cn(
                "mr-3 h-5 w-5 transition-transform duration-200",
                isActive ? "text-white" : "text-gray-500 group-hover:text-blue-500 group-hover:scale-110"
              )} />
              <span className="relative z-10">{item.name}</span>
              {isActive && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-90 rounded-xl"></div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-100">
        <div className="text-xs text-gray-500 text-center bg-gray-50 rounded-lg py-2 px-3">
          <div className="font-medium">Auto Blog SaaS</div>
          <div className="text-gray-400">v1.0.0</div>
        </div>
      </div>
    </div>
  )
}
