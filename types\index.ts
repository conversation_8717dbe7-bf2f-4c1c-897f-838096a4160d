import { Database } from './database'

// 基础类型别名
export type Project = Database['public']['Tables']['projects']['Row']
export type Author = Database['public']['Tables']['authors']['Row']
export type Prompt = Database['public']['Tables']['prompts']['Row']
export type Series = Database['public']['Tables']['series']['Row']
export type Post = Database['public']['Tables']['posts']['Row']
export type SeriesPost = Database['public']['Tables']['series_posts']['Row']

// 扩展类型（包含关联数据）
export type PostWithRelations = Post & {
  author?: Author | null
  series?: Series | null
  prompt?: Prompt | null
}

export type SeriesWithPosts = Series & {
  posts?: Post[]
}

// 表单类型
export type CreateProjectForm = {
  name: string
  description?: string
  database_url?: string
  api_key?: string
}

export type CreateAuthorForm = {
  name: string
  bio?: string
  avatar_url?: string
  email?: string
  website?: string
  social_links?: Record<string, string>
  expertise?: string[]
}

export type CreatePromptForm = {
  name: string
  description?: string
  content: string
  variables?: Record<string, any>
  category?: string
  language?: string
}

export type CreateSeriesForm = {
  name: string
  description?: string
}

export type CreatePostForm = {
  title: string
  content: string
  excerpt?: string
  keywords?: string[]
  language?: string
  meta_title?: string
  meta_description?: string
  meta_keywords?: string
  category?: string
  tags?: string[]
  series_id?: string
  author_id?: string
  prompt_id?: string
}

// 博文生成参数
export type PostGenerationParams = {
  keywords: string[]
  title?: string
  language: string
  series_id?: string
  author_id?: string
  prompt_id?: string
  auto_generate_title?: boolean
  auto_generate_seo?: boolean
  auto_generate_tags?: boolean
  custom_instructions?: string
}

// AI 生成相关类型
export type AIProvider = 'openai' | 'anthropic' | 'google'

export type AIGenerationRequest = {
  prompt: string
  provider?: AIProvider
  model?: string
  temperature?: number
  max_tokens?: number
}

export type AIGenerationResponse = {
  content: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

// 语言选项
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: 'English' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'it', name: 'Italiano' },
  { code: 'pt', name: 'Português' },
  { code: 'ru', name: 'Русский' },
] as const

export type LanguageCode = typeof SUPPORTED_LANGUAGES[number]['code']

// 状态选项
export const POST_STATUSES = [
  { value: 'draft', label: '草稿' },
  { value: 'published', label: '已发布' },
  { value: 'archived', label: '已归档' },
] as const

export const AUTHOR_STATUSES = [
  { value: 'active', label: '活跃' },
  { value: 'inactive', label: '非活跃' },
] as const

export const PROMPT_STATUSES = [
  { value: 'active', label: '活跃' },
  { value: 'inactive', label: '非活跃' },
  { value: 'draft', label: '草稿' },
] as const

export const SERIES_STATUSES = [
  { value: 'active', label: '进行中' },
  { value: 'inactive', label: '暂停' },
  { value: 'completed', label: '已完成' },
] as const

export const PROJECT_STATUSES = [
  { value: 'active', label: '活跃' },
  { value: 'inactive', label: '非活跃' },
] as const

// 分类选项
export const PROMPT_CATEGORIES = [
  { value: 'blog', label: '博文生成' },
  { value: 'seo', label: 'SEO优化' },
  { value: 'title', label: '标题生成' },
  { value: 'summary', label: '摘要生成' },
  { value: 'tags', label: '标签生成' },
] as const

export const POST_CATEGORIES = [
  { value: 'technology', label: '技术' },
  { value: 'business', label: '商业' },
  { value: 'lifestyle', label: '生活方式' },
  { value: 'education', label: '教育' },
  { value: 'health', label: '健康' },
  { value: 'travel', label: '旅行' },
  { value: 'food', label: '美食' },
  { value: 'entertainment', label: '娱乐' },
  { value: 'sports', label: '体育' },
  { value: 'news', label: '新闻' },
] as const

// 工具函数类型
export type FilterOptions<T> = {
  [K in keyof T]?: T[K] | T[K][]
}

export type SortOptions<T> = {
  field: keyof T
  direction: 'asc' | 'desc'
}

export type PaginationOptions = {
  page: number
  limit: number
}

// API 响应类型
export type ApiResponse<T = any> = {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export type PaginatedResponse<T> = ApiResponse<{
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}>

// 错误类型
export class AppError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'AppError'
  }
}
