import { NextRequest, NextResponse } from 'next/server'
import { supabaseService } from '@/lib/supabase'
import { CreateProjectForm } from '@/types'

// 获取项目列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')

    // 模拟数据 - 实际项目中从数据库获取
    const mockProjects = [
      {
        id: 'default',
        name: '默认项目',
        description: '系统默认项目，用于存储初始数据和测试',
        database_url: null,
        api_key: null,
        status: 'active',
        created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: '2',
        name: '生产环境博客',
        description: '主要的生产环境博客项目，包含所有正式发布的内容',
        database_url: 'postgresql://user:password@localhost:5432/blog_prod',
        api_key: 'prod_api_key_***',
        status: 'active',
        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '3',
        name: '测试环境',
        description: '用于测试新功能和内容的测试环境',
        database_url: 'postgresql://user:password@localhost:5432/blog_test',
        api_key: 'test_api_key_***',
        status: 'active',
        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '4',
        name: '归档项目',
        description: '已停用的旧项目，保留历史数据',
        database_url: 'postgresql://user:password@localhost:5432/blog_archive',
        api_key: null,
        status: 'inactive',
        created_at: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      }
    ]

    // 应用筛选
    let filteredProjects = mockProjects
    if (status && status !== 'all') {
      filteredProjects = filteredProjects.filter(p => p.status === status)
    }

    return NextResponse.json({
      success: true,
      data: filteredProjects,
    })
  } catch (error) {
    console.error('获取项目列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目列表失败' },
      { status: 500 }
    )
  }
}

// 创建新项目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const projectData: CreateProjectForm = body

    // 验证必需字段
    if (!projectData.name) {
      return NextResponse.json(
        { success: false, error: '项目名称不能为空' },
        { status: 400 }
      )
    }

    // 验证数据库URL格式（如果提供）
    if (projectData.database_url) {
      try {
        new URL(projectData.database_url)
      } catch {
        return NextResponse.json(
          { success: false, error: '数据库URL格式不正确' },
          { status: 400 }
        )
      }
    }

    // 模拟创建项目
    const newProject = {
      id: Date.now().toString(),
      ...projectData,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    console.log('创建项目:', newProject)

    return NextResponse.json({
      success: true,
      data: newProject,
      message: '项目创建成功',
    })
  } catch (error) {
    console.error('创建项目失败:', error)
    return NextResponse.json(
      { success: false, error: '创建项目失败' },
      { status: 500 }
    )
  }
}
