'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  MessageSquare, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Copy,
  Star,
  TrendingUp
} from 'lucide-react'
import { Prompt, PROMPT_STATUSES, PROMPT_CATEGORIES } from '@/types'
import { formatDate, timeAgo } from '@/lib/utils'

export default function PromptsPage() {
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')

  // 获取Prompt列表
  const fetchPrompts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        project_id: 'default',
      })
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }
      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter)
      }

      const response = await fetch(`/api/prompts?${params}`)
      const result = await response.json()
      
      if (result.success) {
        setPrompts(result.data || [])
      } else {
        console.error('获取Prompt失败:', result.error)
      }
    } catch (error) {
      console.error('获取Prompt失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPrompts()
  }, [statusFilter, categoryFilter])

  // 删除Prompt
  const deletePrompt = async (id: string) => {
    if (!confirm('确定要删除这个Prompt吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/prompts/${id}`, {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        setPrompts(prompts.filter(prompt => prompt.id !== id))
        alert('Prompt删除成功')
      } else {
        alert('删除失败：' + result.error)
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 复制Prompt
  const copyPrompt = async (prompt: Prompt) => {
    try {
      const response = await fetch('/api/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...prompt,
          name: `${prompt.name} (副本)`,
          status: 'draft',
        }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        fetchPrompts() // 重新获取列表
        alert('Prompt复制成功')
      } else {
        alert('复制失败：' + result.error)
      }
    } catch (error) {
      console.error('复制失败:', error)
      alert('复制失败，请重试')
    }
  }

  // 过滤Prompt
  const filteredPrompts = prompts.filter(prompt => {
    const matchesSearch = prompt.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         prompt.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         prompt.content.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesSearch
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '活跃', className: 'status-badge active' },
      inactive: { label: '非活跃', className: 'status-badge inactive' },
      draft: { label: '草稿', className: 'status-badge draft' },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <span className={config.className}>{config.label}</span>
  }

  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      blog: { label: '博文生成', color: 'bg-blue-100 text-blue-800' },
      seo: { label: 'SEO优化', color: 'bg-green-100 text-green-800' },
      title: { label: '标题生成', color: 'bg-purple-100 text-purple-800' },
      summary: { label: '摘要生成', color: 'bg-orange-100 text-orange-800' },
      tags: { label: '标签生成', color: 'bg-pink-100 text-pink-800' },
    }
    
    const config = categoryConfig[category as keyof typeof categoryConfig]
    if (!config) return null
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${config.color}`}>
        {config.label}
      </span>
    )
  }

  return (
    <MainLayout title="Prompt管理" subtitle="管理您的AI生成模板">
      <div className="space-y-6">
        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            {/* 搜索 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索Prompt..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            {/* 筛选 */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {PROMPT_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                {PROMPT_CATEGORIES.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Link href="/prompts/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              创建Prompt
            </Button>
          </Link>
        </div>

        {/* Prompt列表 */}
        <div className="card">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="loading-spinner"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : filteredPrompts.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无Prompt</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== 'all' || categoryFilter !== 'all' 
                  ? '没有找到匹配的Prompt' 
                  : '开始创建您的第一个Prompt模板'}
              </p>
              <Link href="/prompts/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  创建Prompt
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPrompts.map((prompt) => (
                <div key={prompt.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  {/* 头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-1">
                        {prompt.name}
                      </h3>
                      {prompt.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {prompt.description}
                        </p>
                      )}
                    </div>
                    {getStatusBadge(prompt.status)}
                  </div>

                  {/* 分类和使用次数 */}
                  <div className="flex items-center justify-between mb-4">
                    {prompt.category && getCategoryBadge(prompt.category)}
                    <div className="flex items-center text-sm text-gray-500">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {prompt.usage_count || 0} 次使用
                    </div>
                  </div>

                  {/* 内容预览 */}
                  <div className="bg-gray-50 rounded p-3 mb-4">
                    <p className="text-sm text-gray-700 line-clamp-3">
                      {prompt.content}
                    </p>
                  </div>

                  {/* 元信息 */}
                  <div className="text-xs text-gray-500 mb-4">
                    <div>创建时间：{formatDate(prompt.created_at)}</div>
                    <div>更新时间：{timeAgo(prompt.updated_at)}</div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Link href={`/prompts/${prompt.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/prompts/${prompt.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => copyPrompt(prompt)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => deletePrompt(prompt.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 统计信息 */}
        {!loading && filteredPrompts.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            共 {filteredPrompts.length} 个Prompt
            {searchQuery && ` · 搜索"${searchQuery}"`}
            {statusFilter !== 'all' && ` · 状态：${PROMPT_STATUSES.find(s => s.value === statusFilter)?.label}`}
            {categoryFilter !== 'all' && ` · 分类：${PROMPT_CATEGORIES.find(c => c.value === categoryFilter)?.label}`}
          </div>
        )}
      </div>
    </MainLayout>
  )
}
