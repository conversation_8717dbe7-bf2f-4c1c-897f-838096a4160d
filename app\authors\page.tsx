'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  User, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Mail,
  Globe,
  Calendar,
  FileText
} from 'lucide-react'
import { Author, AUTHOR_STATUSES } from '@/types'
import { formatDate, timeAgo } from '@/lib/utils'

export default function AuthorsPage() {
  const [authors, setAuthors] = useState<Author[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // 获取作者列表
  const fetchAuthors = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        project_id: 'default',
      })
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/authors?${params}`)
      const result = await response.json()
      
      if (result.success) {
        setAuthors(result.data || [])
      } else {
        console.error('获取作者失败:', result.error)
      }
    } catch (error) {
      console.error('获取作者失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAuthors()
  }, [statusFilter])

  // 删除作者
  const deleteAuthor = async (id: string) => {
    if (!confirm('确定要删除这个作者吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/authors/${id}`, {
        method: 'DELETE',
      })
      
      const result = await response.json()
      
      if (result.success) {
        setAuthors(authors.filter(author => author.id !== id))
        alert('作者删除成功')
      } else {
        alert('删除失败：' + result.error)
      }
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 更新作者状态
  const updateAuthorStatus = async (id: string, status: string) => {
    try {
      const response = await fetch(`/api/authors/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        setAuthors(authors.map(author => 
          author.id === id ? { ...author, status } : author
        ))
        alert('状态更新成功')
      } else {
        alert('更新失败：' + result.error)
      }
    } catch (error) {
      console.error('更新失败:', error)
      alert('更新失败，请重试')
    }
  }

  // 过滤作者
  const filteredAuthors = authors.filter(author => {
    const matchesSearch = author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         author.bio?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         author.email?.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesSearch
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '活跃', className: 'status-badge active' },
      inactive: { label: '非活跃', className: 'status-badge inactive' },
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active
    return <span className={config.className}>{config.label}</span>
  }

  return (
    <MainLayout title="作者管理" subtitle="管理您的博文作者信息">
      <div className="space-y-6">
        {/* 操作栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            {/* 搜索 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索作者..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            {/* 筛选 */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {AUTHOR_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Link href="/authors/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加作者
            </Button>
          </Link>
        </div>

        {/* 作者列表 */}
        <div className="card">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="loading-spinner"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : filteredAuthors.length === 0 ? (
            <div className="text-center py-12">
              <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无作者</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== 'all' 
                  ? '没有找到匹配的作者' 
                  : '开始添加您的第一个作者'}
              </p>
              <Link href="/authors/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  添加作者
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAuthors.map((author) => (
                <div key={author.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  {/* 头像和基本信息 */}
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="flex-shrink-0">
                      {author.avatar_url ? (
                        <img
                          src={author.avatar_url}
                          alt={author.name}
                          className="h-12 w-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {author.name}
                        </h3>
                        {getStatusBadge(author.status)}
                      </div>
                      {author.bio && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {author.bio}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 联系信息 */}
                  <div className="space-y-2 mb-4">
                    {author.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="truncate">{author.email}</span>
                      </div>
                    )}
                    {author.website && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Globe className="h-4 w-4 mr-2 flex-shrink-0" />
                        <a 
                          href={author.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="truncate hover:text-blue-600"
                        >
                          {author.website}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* 专业领域 */}
                  {author.expertise && Array.isArray(author.expertise) && author.expertise.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {author.expertise.slice(0, 3).map((skill) => (
                          <span
                            key={skill}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                          >
                            {skill}
                          </span>
                        ))}
                        {author.expertise.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{author.expertise.length - 3} 更多
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 元信息 */}
                  <div className="text-xs text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      创建于 {formatDate(author.created_at)}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Link href={`/authors/${author.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/authors/${author.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => updateAuthorStatus(
                          author.id, 
                          author.status === 'active' ? 'inactive' : 'active'
                        )}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        {author.status === 'active' ? '停用' : '启用'}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => deleteAuthor(author.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 统计信息 */}
        {!loading && filteredAuthors.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            共 {filteredAuthors.length} 个作者
            {searchQuery && ` · 搜索"${searchQuery}"`}
            {statusFilter !== 'all' && ` · 状态：${AUTHOR_STATUSES.find(s => s.value === statusFilter)?.label}`}
          </div>
        )}
      </div>
    </MainLayout>
  )
}
